from datetime import datetime, time, timedelta

import django_filters
import pytz
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.measure import D
from django.core.cache import cache
from django.utils import timezone

from longbeach.utils.search import term_to_query

from .models import ContentType, UGCCategory, UGCMasthead, UserGeneratedContent


class DateRangeOptions:
    TODAY = "today"
    THIS_WEEKEND = "this_weekend"
    THIS_WEEK = "this_week"
    NEXT_WEEK = "next_week"
    NEXT_30_DAYS = "next_30_days"


class NumberInFilter(django_filters.BaseInFilter, django_filters.NumberFilter):
    pass


class UserGeneratedContentFilter(django_filters.FilterSet):
    site_id = django_filters.CharFilter(
        method="filter_by_masthead", required=False
    )
    publishable = django_filters.BooleanFilter(method="filter_by_publishable")
    content_types = django_filters.CharFilter(method="filter_by_content_types")
    id__in = NumberInFilter(field_name="id", lookup_expr="in")
    id_not_in = NumberInFilter(field_name="id", lookup_expr="in", exclude=True)
    q = django_filters.CharFilter(method="filter_by_query")
    date_range = django_filters.CharFilter(method="filter_by_date_range")
    category = django_filters.ModelChoiceFilter(
        queryset=UGCCategory.objects.all()
    )
    location = django_filters.CharFilter(lookup_expr="icontains")
    distance_km = django_filters.NumberFilter(
        method="filter_by_distance_from_masthead", initial=10
    )

    class Meta:
        model = UserGeneratedContent
        fields = ("masthead_id", "content_types", "id", "category", "location")

    def filter_by_masthead(self, queryset, name, value):
        if not value or value.lower() == "none":
            return queryset

        masthead_ids = value.split(",")
        self._current_masthead_id = masthead_ids[-1]
        return queryset.filter(masthead__in=masthead_ids)

    def filter_by_publishable(self, queryset, name, value):
        self._publishable_value = value
        if value:
            return queryset.filter(status="approved")
        return queryset.filter(status__in=["pending", "rejected"])

    def filter_by_content_types(self, queryset, name, value):
        content_types = value.split(",")
        queryset = queryset.filter(content_type__in=content_types)

        # Apply 200km distance filter for events when masthead_id is present and content type is event only
        if len(content_types) == 1 and ContentType.EVENT in content_types:
            masthead_id = getattr(self, "_current_masthead_id", None)
            if masthead_id:
                try:
                    masthead = UGCMasthead.objects.get(site_id=masthead_id)
                    reference_location = masthead.location_point
                    if reference_location:
                        queryset = queryset.filter(
                            location_point__distance_lte=(
                                reference_location,
                                D(km=200),
                            )
                        ).annotate(
                            distance=Distance("location_point", reference_location)
                        )
                except UGCMasthead.DoesNotExist:
                    pass

        return queryset

    def filter_by_query(self, queryset, name, value):
        return queryset.filter(term_to_query(value, {"title"}))

    def filter_by_date_range(self, queryset, name, value):
        """
        Filter by predefined date ranges:
        - Today
        - This Weekend
        - This Week
        - Next Week
        - Next 30 Days
        """
        australia_tz = pytz.timezone("Australia/Sydney")
        today = timezone.now().astimezone(australia_tz).date()

        formatted_value = value.lower().replace(" ", "_")
        date_range_start = None
        date_range_end = None

        if formatted_value == DateRangeOptions.TODAY:
            return queryset.filter(next_occurrence__date=today)

        elif formatted_value == DateRangeOptions.THIS_WEEKEND:
            days_until_saturday = (5 - today.weekday()) % 7
            saturday = today + timedelta(days=days_until_saturday)
            sunday = saturday + timedelta(days=1)
            date_range_start = saturday
            date_range_end = sunday

        elif formatted_value == DateRangeOptions.THIS_WEEK:
            start_of_week = today - timedelta(days=today.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            date_range_start = start_of_week
            date_range_end = end_of_week

        elif formatted_value == DateRangeOptions.NEXT_WEEK:
            start_of_next_week = today + timedelta(days=(7 - today.weekday()))
            end_of_next_week = start_of_next_week + timedelta(days=6)
            date_range_start = start_of_next_week
            date_range_end = end_of_next_week

        elif formatted_value == DateRangeOptions.NEXT_30_DAYS:
            thirty_days_later = today + timedelta(days=30)
            date_range_start = today
            date_range_end = thirty_days_later

        if date_range_start and date_range_end:
            non_reccurring_events = queryset.filter(
                recurrences__isnull=True,
                end_datetime__date__gte=date_range_start,
                start_datetime__date__lte=date_range_end,
            )

            date_range_start = datetime.combine(date_range_start, time.min)
            date_range_end = datetime.combine(date_range_end, time.max)

            cache_key = f"recurring_event_ids_{date_range_start.isoformat()}_{date_range_end.isoformat()}"
            recurring_events_ids = cache.get(cache_key)

            if recurring_events_ids is None:
                recurring_events_ids = []
                filters = {
                    "recurrences__isnull": False,
                }
                if getattr(self, "_publishable_value", None):
                    filters["status"] = UserGeneratedContent.APPROVED
                for event in (
                    UserGeneratedContent.objects.filter(**filters)
                    .exclude(start_datetime__gt=date_range_end)
                    .iterator()
                ):
                    occurrences = event.recurrences.between(
                        date_range_start, date_range_end, inc=True
                    )
                    if occurrences:
                        recurring_events_ids.append(event.id)

                cache.set(cache_key, recurring_events_ids, timeout=600)

            recurring_events = queryset.filter(
                recurrences__isnull=False, id__in=recurring_events_ids
            )

            return non_reccurring_events | recurring_events

        return queryset

    def filter_by_distance_from_masthead(self, queryset, name, value):
        site_id = getattr(self, "_current_masthead_id", None)

        if not site_id:
            return queryset

        try:
            masthead = UGCMasthead.objects.get(site_id=site_id)
        except UGCMasthead.DoesNotExist:
            return queryset

        try:
            reference_location = masthead.location_point
            radius_km = float(self.data.get("distance_km"))

            return (
                queryset.filter(
                    location_point__distance_lte=(
                        reference_location,
                        D(km=radius_km),
                    )
                )
                .annotate(
                    distance=Distance("location_point", reference_location)
                )
                .order_by("distance")
            )

        except (TypeError, ValueError):
            return queryset

    @property
    def qs(self):
        today = timezone.now()
        queryset = super().qs

        content_types = self.data.get("content_types", "").split(",")

        # filter out past dates and sort by start date if content_type param only contains 'event'
        if len(content_types) == 1 and ContentType.EVENT in content_types:
            queryset = queryset.filter(
                next_occurrence__isnull=False, end_datetime__gte=today
            ).order_by("next_occurrence")

        # sort by most recently published if content_type param only contains 'photos'
        if len(content_types) == 1 and ContentType.PHOTOS in content_types:
            queryset = queryset.order_by("-published_on")

        return queryset
